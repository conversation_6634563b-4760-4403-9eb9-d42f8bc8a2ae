package com.cmcc.jk.jdbc.web.server;

import com.sun.net.httpserver.*;
import org.json.JSONObject;

import java.io.*;
import javax.net.ssl.*;
import java.net.InetSocketAddress;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.util.Base64;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

//-j ************ -U clark -P G00d.H0pe -p 6000
//--jdbc ************ --user test --pass DEM0@g10ba1

public class JdbcWebServerMain {

    public static final String BEARER = null;
    public static final String REMOTE = "http://localhost:6000";
    public static final String PASSWORD = "DEM0@g10ba1";
    public static final int PORT = 5000;
    public static final String HOST = "0.0.0.0";
//    public static final String JDBC = "********************************";
//    public static final String USER = "admin";
//    public static final String PASS = "mysql@123";
    public static final String JDBC = "********************************";
    public static final String USER = "root";
    public static final String PASS = "mysql8@160";
    public static final boolean SAFE = false;

    public static final String DELIMITER = ",";

    public static boolean safe = SAFE;
    public static String host = HOST;
    public static int port = 0;
    public static String jdbc = JDBC;
    public static String user = USER;
    public static String pass = PASS;
    public static String delimiter = DELIMITER;

    public static boolean debug = false;
    public static String bearer = BEARER;
    public static String remote = REMOTE;

    public static String toStr(final InputStream inputStream) throws IOException {
        final byte[] byteArray = toByteArray(inputStream);
        final String string = new String(byteArray, StandardCharsets.UTF_8);
        return string;
    }

    public static byte[] toByteArray(final InputStream inputStream) throws IOException {
        try (final ByteArrayOutputStream baos = new ByteArrayOutputStream();) {
            final byte[] buffer = new byte[4096];
            int n = 0;
            while (-1 != (n = inputStream.read(buffer))) {
                baos.write(buffer, 0, n);
            }
            final byte[] byteArray = baos.toByteArray();
            return byteArray;
        }
    }

    public static String getBody(HttpExchange exchange) throws IOException {
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
//            StringBuilder content = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                content.append(line);
//                content.append(System.lineSeparator()); // 添加换行符，保留原始格式
//            }
//            String result = content.toString();
//            System.out.printf("request:\n%s\n", result);
//            return result;
//        }
        String result = toStr(exchange.getRequestBody());
        if (debug)
            System.out.printf("request:\n%s\n", result);
        return result;
    }

    public static final String THINK_HEAD = "<think>";
    public static final String THINK_TAIL = "</think>";
    public static final String BLOCK_HEAD = "```sql\n";
    public static final String BLOCK_TAIL = "\n```";

    public static String extractSQL(String text) {
        if (text.startsWith(THINK_HEAD)) {
            int index = text.indexOf(THINK_TAIL);

            if (index >= 0) {
                int bytes = index + THINK_TAIL.length();
                text = text.substring(bytes);
                System.out.printf("[SQL Handler] Remove Think Chain: %d char(s):\n", bytes);
            } else {
                System.out.printf("[SQL Handler] Miss Think Tail: %s\n", text);
                return null;
            }
        }

        int head = text.indexOf(BLOCK_HEAD);
        if (head >= 0) {
            head += BLOCK_HEAD.length();
            int tail = text.indexOf(BLOCK_TAIL, BLOCK_HEAD.length());
            if (tail >= 0) {
                text = text.substring(head, tail);
                System.out.printf("[SQL Handler] Extract SQL Block from %d to %d\n", head, tail);
            } else {
                System.out.printf("[SQL Handler] Miss Block Tail: %s\n", text);
                return null;
            }
        }

        return text.trim();
    }

    public static ArrayList<String> runQuery(final Connection connection, final String sql) throws SQLException {
        if (!sql.toLowerCase().startsWith("select"))
            throw new SQLException(String.format("Only SELECT is supported: %s", sql));

        ArrayList<String> content = new ArrayList<>();

        try (final Statement statement = connection.createStatement();) {
            try (final ResultSet resultSet = statement.executeQuery(sql);) {
                final ResultSetMetaData metaData = resultSet.getMetaData();
                final int columnCount = metaData.getColumnCount();
                final List<String> columnLabelList = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    final String columnLabel = metaData.getColumnLabel(i);
                    columnLabelList.add(columnLabel.trim());
                }

                final String header = columnLabelList.stream().collect(Collectors.joining(DELIMITER)).trim();
                if (header.isEmpty()) {
                    System.out.printf("[SQL execute] No column name: %s\n", sql);
                    return null;
                }
                content.add(header);

                while (resultSet.next()) {
                    final List<Object> columnListOneRow = new ArrayList<>();
                    for (int i = 1; i <= columnCount; i++) {
                        final Object columnValue = resultSet.getObject(i);
                        columnListOneRow.add(columnValue);
                    }
                    final String row = columnListOneRow.stream().map(Objects::toString).collect(Collectors.joining(DELIMITER)).trim();

                    content.add(row);
                }

				if (content.size() == 1) {
					content.set(0, "未查询到数据")
				}

                return content;
            }
        }
    }

    static boolean parseArgs(String[] args) {
        for(int i = 0; i < args.length;) {
            String arg = args[i++];
            switch (arg) {
                case "-h":
                case "--host":
                    if (i < args.length)
                        host = args[i++];
                    else
                        return false;
                    break;
                case "-p":
                case "--port":
                    if (i < args.length)
                        port = Integer.parseInt(args[i++]);
                    else
                        return false;
                    break;
                case "-j":
                case "--jdbc":
                    if (i < args.length)
                        jdbc = String.format("jdbc:mysql://%s", args[i++]);
                    else
                        return false;
                    break;
                case "-U":
                case "--user":
                    if (i < args.length)
                        user = args[i++];
                    else
                        return false;
                    break;
                case "-P":
                case "--pass":
                    if (i < args.length)
                        pass = args[i++];
                    else
                        return false;
                    break;
                case "-d":
                case "--delimiter":
                    if (i < args.length) {
                        switch(args[i++].toLowerCase()) {
                            case "comma":
                                delimiter = ",";
                                break;
                            case "tab":
                                delimiter = "\t";
                                break;
                            case "pipe":
                                delimiter = "|";
                                break;
                            default:
                                return false;
                        }

                    } else
                        return false;
                    break;
                case "-b":
                case "--bearer":
                    if (i < args.length)
                        bearer = args[i++];
                    else
                        return false;
                    break;
                case "-r":
                case "--remote":
                    if (i < args.length)
                        remote = args[i++];
                    else
                        return false;
                    break;
                case "-l":
                case "--log":
                    debug = true;
                    break;
                case "-s":
                case "--safe":
                    safe = true;
                    break;
                default:
                    return false;
            }
        }

        if (port == 0)
            port = safe ? 443 : 80;
        return true;
    }

    static Connection connection = null;

    public static void sendResultWithStatus(String response, HttpExchange exchange, int statusCode) throws IOException {
        if (debug)
            System.out.printf("response:\n%s\n", response);

        Headers headers = exchange.getResponseHeaders();
        headers.set("Content-Type", "text/plain; charset=UTF-8");

        final byte[] responseBodyBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBodyBytes.length);
        try (final OutputStream responseBodyOs = exchange.getResponseBody();) {
            responseBodyOs.write(responseBodyBytes);
        }
    }

    public static Connection getConnection() {
        try {
            // It's good practice to use try-with-resources for the Connection if it's only used within main's scope
            // However, since it's shared across requests, it needs to be managed carefully for closure.
            // For a simple server like this, closing it on shutdown (e.g., via a shutdown hook) would be ideal.
            // Class.forName("com.mysql.cj.jdbc.Driver").newInstance(); // newInstance() is deprecated and not needed for modern JDBC
            Class.forName("com.mysql.cj.jdbc.Driver");  //  .newInstance();

            return DriverManager.getConnection(jdbc, user, pass);
        } catch (SQLException e) {
            System.err.printf("Failed to connect to database: %s\n", e.getMessage());
            return null;
        } catch (ClassNotFoundException e) {
            System.err.printf("Failed to load JDBC driver: %s\n", e.getMessage());
            return null;
        }
    }

    public static boolean startup() {
        connection = getConnection();
        if (connection == null) {
            System.err.printf("Failed to startup\n");
            return false;
        }

        HttpServer httpServer = safe ? createHTTPs() : createHTTP();
        setupHandlers(httpServer);

        httpServer.setExecutor(java.util.concurrent.Executors.newCachedThreadPool()); // Optional: for better performance with multiple requests
        httpServer.start();
        System.out.printf("Server is listening on %s:%d\n", host, port);

        // Add a shutdown hook to close the database connection
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("Shutting down server...");
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("Database connection closed.");
                } catch (SQLException e) {
                    System.err.println("Error closing database connection: " + e.getMessage());
                }
            }
            if (httpServer != null)
                httpServer.stop(0); // 0 means stop immediately
            System.out.println("Server stopped.");
        }));

        return true;
    }

    public static void setupHandlers(HttpServer httpServer) {
        httpServer.createContext("/query", new SQLHandler());
        httpServer.createContext("/info", new TXTHandler());

        // New handler for /llm/* POST requests
        httpServer.createContext("/llm", new LLMHandler());
    }

    public static HttpServer createHTTP() {
        try {
            return HttpServer.create(new InetSocketAddress(host, port), 0);
        } catch (IOException e) {
            System.err.printf("Failed to create HTTP server: %s\n", e.getMessage());
            return null;
        }
    }

    public static HttpServer createHTTPs() {
        try {
            HttpsServer  httpsServer = HttpsServer.create(new InetSocketAddress(host, port), 0);
            // 配置SSL
            // 配置SSL
            SSLContext sslContext = createSSLContext();
            httpsServer.setHttpsConfigurator(new HttpsConfigurator(sslContext) {
                @Override
                public void configure(HttpsParameters params) {
                    try {
                        SSLContext context = getSSLContext();
                        SSLEngine engine = context.createSSLEngine();

                        params.setNeedClientAuth(false);
                        params.setCipherSuites(engine.getEnabledCipherSuites());
                        params.setProtocols(new String[]{"TLSv1.2", "TLSv1.3"});
                        params.setSSLParameters(context.getDefaultSSLParameters());

                    } catch (Exception e) {
                        System.err.println("Failed to configure HTTPS: " + e.getMessage());
                    }
                }
            });
            return httpsServer;
        } catch (IOException e) {
            System.err.printf("Failed to create HTTP server: %s\n", e.getMessage());
            return null;
        } catch (Exception e) {
            System.err.printf("Failed to create HTTP server: %s\n", e.getMessage());
            return null;
        }
    }

    private static SSLContext createSSLContext() throws Exception {
        KeyStore keyStore = KeyStore.getInstance("JKS");
        try (FileInputStream fis = new FileInputStream("cfg/nexlify.top.jks")) {
            keyStore.load(fis, PASSWORD.toCharArray());
        }

        KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
        kmf.init(keyStore, PASSWORD.toCharArray());

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(kmf.getKeyManagers(), null, null);

        return sslContext;
    }

//    private static SSLContext createSSLContext() throws Exception {
//        // 方法1: 从KeyStore文件加载
//        return createSSLContextFromKeyStore();
//
//        // 方法2: 从PEM文件动态加载
//        // return createSSLContextFromPEM();
//    }
//
//    // 方法1: 从KeyStore文件加载SSL证书
//    private static SSLContext createSSLContextFromKeyStore() throws Exception {
//        // 加载KeyStore
//        KeyStore keyStore = KeyStore.getInstance("PKCS12");
//        try (FileInputStream fis = new FileInputStream("cfg/nexlify.top.p12")) {
//            keyStore.load(fis, PASSWORD.toCharArray());
//        }
//
//        // 创建KeyManagerFactory
//        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
//        kmf.init(keyStore, PASSWORD.toCharArray());
//
//        // 创建TrustManagerFactory（可选，用于客户端认证）
//        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
//        tmf.init(keyStore);
//
//        // 创建SSLContext
//        SSLContext sslContext = SSLContext.getInstance("TLS");
//        sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);
//
//        return sslContext;
//    }
//
//    // 方法2: 从PEM文件动态加载SSL证书
//    private static SSLContext createSSLContextFromPEM() throws Exception {
//        // 读取私钥文件
//        PrivateKey privateKey = loadPrivateKey("private.key");
//
//        // 读取证书文件
//        Certificate[] certChain = loadCertificateChain("fullchain.cer");
//
//        // 创建KeyStore
//        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
//        keyStore.load(null, null);
//        keyStore.setKeyEntry("server", privateKey, PASSWORD.toCharArray(), certChain);
//
//        // 创建KeyManagerFactory
//        KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
//        kmf.init(keyStore, PASSWORD.toCharArray());
//
//        // 加载CA证书作为信任库（可选）
//        KeyStore trustStore = loadTrustStore("ca.cer");
//        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
//        tmf.init(trustStore);
//
//        // 创建SSLContext
//        SSLContext sslContext = SSLContext.getInstance("TLS");
//        sslContext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);
//
//        return sslContext;
//    }
//
//    // 加载私钥文件
//    private static PrivateKey loadPrivateKey(String keyPath) throws Exception {
//        String keyContent = new String(Files.readAllBytes(Paths.get(keyPath)));
//
//        // 移除PEM头尾标记
//        keyContent = keyContent.replace("-----BEGIN PRIVATE KEY-----", "")
//                .replace("-----END PRIVATE KEY-----", "")
//                .replace("-----BEGIN RSA PRIVATE KEY-----", "")
//                .replace("-----END RSA PRIVATE KEY-----", "")
//                .replaceAll("\\s", "");
//
//        byte[] keyBytes = Base64.getDecoder().decode(keyContent);
//        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
//        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
//
//        return keyFactory.generatePrivate(spec);
//    }
//
//    // 加载证书链
//    private static Certificate[] loadCertificateChain(String certPath) throws Exception {
//        CertificateFactory cf = CertificateFactory.getInstance("X.509");
//        try (FileInputStream fis = new FileInputStream(certPath)) {
//            return cf.generateCertificates(fis).toArray(new Certificate[0]);
//        }
//    }
//
//    // 加载信任库
//    private static KeyStore loadTrustStore(String caPath) throws Exception {
//        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
//        trustStore.load(null, null);
//
//        CertificateFactory cf = CertificateFactory.getInstance("X.509");
//        try (FileInputStream fis = new FileInputStream(caPath)) {
//            Certificate caCert = cf.generateCertificate(fis);
//            trustStore.setCertificateEntry("ca", caCert);
//        }
//
//        return trustStore;
//    }

    static class TXTHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) {
            // 获取当前日期
            java.time.LocalDate currentDate = java.time.LocalDate.now();
            String current = currentDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 获取上个月1号
            java.time.LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).withDayOfMonth(1);
            String previews = firstDayOfLastMonth.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            JSONObject json = new JSONObject();
            json.put("client", exchange.getRemoteAddress().getHostString());
            json.put("current", current);
            json.put("previous", previews);
            try {
                sendResultWithStatus(json.toString(), exchange, 200);
			} catch (IOException e) {
                System.err.printf("[TXT Handler] %s\n", e.getMessage());
			}
		}
    }

    static class SQLHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) {
            try {
                long b = System.currentTimeMillis();
                // 检查 Authorization header
                if (auth(exchange)) return;

                String requestBody = getBody(exchange);
                try {
                    String sql = extractSQL(requestBody.trim());
                    if (sql == null) {
                        sendResultWithStatus("Invalid Text Format", exchange, 400);
                        return;
                    }
                    sql = sql.trim();
                    if (sql.equals("")) {
                        sendResultWithStatus("SQL statement not found", exchange, 400);
                        return;
                    }

                    System.out.printf("[SQL Handler] SQL: %s\n", sql);

                    Connection connection = getConnection();
                    if (connection == null) {
                        System.err.printf("Failed to get database connection\n");
                        sendResultWithStatus("Failed to get database connection", exchange, 500);
                        return;
                    }
                    try {
                        ArrayList<String> response = runQuery(connection, sql);    // 执行SQL
                        if (response == null || response.isEmpty()) {
                            System.err.printf("The SQL query result is empty\n", exchange, 500);
                            sendResultWithStatus("The SQL query result is empty", exchange, 500);
                            return;
                        }
                        StringBuilder sb = new StringBuilder();
                        for (String s : response) {
//                        System.out.printf("[SQL Handler] Result: %s\n", s);
                            if (sb.length() > 0)
                                sb.append("\n");
                            sb.append(s);
                        }
                        String result = sb.toString();

                        JSONObject json = new JSONObject();

                        json.put("sql", sql);
                        json.put("result", sb.toString());
//                      json.put("client", exchange.getRemoteAddress().getHostString());
                        String content = json.toString();

                        sendResultWithStatus(content, exchange, 200);
                    } finally {
                        connection.close();
                    }

                    long e = System.currentTimeMillis();
                    System.out.printf("[SQL Handler] Used %.3fs query SQL\n", 1e-3*(e-b));
                } catch (final SQLException e) {
                    sendResultWithStatus(String.format("Error SQL: %d - %s", e.getErrorCode(), e.getMessage()), exchange, 500);
                }
            } catch (final IOException e) {
                System.err.printf("[SQL Handler] %s\n", e.getMessage());
            }
        }

        private static boolean auth(HttpExchange exchange) throws IOException {
            if (bearer != null) {
                String authHeader = exchange.getRequestHeaders().getFirst("Authorization");
                if (authHeader == null || !authHeader.equals("Bearer " + bearer)) {
                    sendResultWithStatus("Unauthorized - Invalid or missing bearer token", exchange, 401);
                    return true;
                }
            }
            if (!"POST".equalsIgnoreCase(exchange.getRequestMethod())) {
                sendResultWithStatus("Method Not Allowed. Please use POST for /llm paths.", exchange, 405);
                return true;
            }
            return false;
        }
    }

    static class LLMHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) {
            try {
                long b = System.currentTimeMillis();

                // 检查 Authorization header
                if (SQLHandler.auth(exchange)) return;

                String requestBody = toStr(exchange.getRequestBody());
                String requestPath = exchange.getRequestURI().getPath();
                String queryParams = exchange.getRequestURI().getQuery();

                if (debug) {
                    System.out.printf("[LLM Handler] Received request for path: %s\n", requestPath);
                    if (queryParams != null && !queryParams.isEmpty())
                        System.out.printf("[LLM Handler] URL Query Parameters: %s\n", queryParams);
                    else
                        System.out.println("[LLM Handler] No URL Query Parameters.");
                    System.out.printf("[LLM Handler] Request Body:\n%s\n", requestBody);
                }

                String targetUrlString = requestPath;
                if (queryParams != null && !queryParams.isEmpty()) {
                    targetUrlString += "?" + queryParams; // Append query params if they exist
                }
                URL url = new URL(remote + targetUrlString);

                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setDoOutput(true);
                conn.setConnectTimeout(5000);
                conn.setReadTimeout(10000);

                String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
                if (contentType != null) {
                    conn.setRequestProperty("Content-Type", contentType);
                    if (debug)
                        System.out.printf("[LLM Handler] Forwarding with Content-Type: %s\n", contentType);
                } else {
                    conn.setRequestProperty("Content-Type", "text/plain; charset=UTF-8");
                    if (debug)
                        System.out.println("[LLM Handler] Forwarding with default Content-Type: text/plain; charset=UTF-8");
                }

                try (OutputStream os = conn.getOutputStream()) {
                    os.write(requestBody.getBytes(StandardCharsets.UTF_8));
                }

                int responseCode = conn.getResponseCode();
                String responseContentType = conn.getContentType();
                InputStream is = (responseCode >= 200 && responseCode < 300) ? conn.getInputStream() : conn.getErrorStream();
                String responseBody = toStr(is);

                if (debug) {
                    System.out.printf("[LLM Handler] Forwarded request to %s returned %d\n", targetUrlString, responseCode);
                    System.out.printf("[LLM Handler] Forwarded response Content-Type: %s\n", responseContentType);
                    System.out.printf("[LLM Handler] Forwarded response Body:\n%s\n", responseBody);
                }

                if (contentType != null)
                    exchange.getResponseHeaders().set("Content-Type", responseContentType);

                long e = System.currentTimeMillis();
                sendResultWithStatus(responseBody, exchange, responseCode);
                System.out.printf("[LLM Handler] Used %.3fs forward %d to %d @ %s\n", 1e-3*(e-b), requestBody.length(), responseBody.length(), targetUrlString);
            } catch (final IOException e) {
                System.err.println("[LLM Handler] IO Error handling /llm: " + e.getMessage());
                try {
                    sendResultWithStatus("Internal Server Error during LLM processing", exchange, 500);
                } catch (IOException ex) {
                    System.err.println("[LLM Handler] Failed to send 500 error response for /llm: " + ex.getMessage());
                }
            } catch (final Exception e) { // Catch any other unexpected errors
                System.err.println("[LLM Handler] Unexpected error handling /llm: " + e.getMessage());
                try {
                    sendResultWithStatus("Internal Server Error", exchange, 500);
                } catch (IOException ex) {
                    System.err.println("[LLM Handler] Failed to send 500 error response for /llm: " + ex.getMessage());
                }
            } finally {
                exchange.close(); // Ensure the exchange is always closed
            }
        }
    }

    public static void main(String[] args) throws Throwable {
//        System.out.println(System.getProperty("user.dir"));
//        System.out.println(Paths.get(".").toAbsolutePath().normalize().toString());
        if (!parseArgs(args)) {
            System.err.println("Invalid args. Usage: java -jar jdbc-web-server.jar [-h <host>] [-s <port>] [-j <jdbc_host:port/db>] [-u <user>] [-p <pass>] [-d <comma|tab|pipe>]");
            return;
        }

        System.out.printf("Start @ %s:%d jdbc %s with delimiter '%s'\n", host, port, jdbc, delimiter.replace("\t", "\\t"));
        System.out.flush();
        startup();
    }

}
