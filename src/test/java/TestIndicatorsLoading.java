import com.cmcc.jk.jdbc.web.server.JdbcWebServerMain;
import java.util.HashMap;

/**
 * Simple test class to verify indicators loading functionality
 */
public class TestIndicatorsLoading {
    
    public static void main(String[] args) {
        System.out.println("Testing indicators loading...");
        
        // Enable debug mode for more detailed output
        JdbcWebServerMain.debug = true;
        
        // Test loading indicators
        boolean success = JdbcWebServerMain.loadIndicators();
        
        if (success) {
            System.out.println("✓ Indicators loaded successfully!");
            
            // Get all indicators and display them
            HashMap<String, JdbcWebServerMain.Indicator> allIndicators = JdbcWebServerMain.getAllIndicators();
            
            System.out.println("\nLoaded indicators:");
            for (String fieldName : allIndicators.keySet()) {
                JdbcWebServerMain.Indicator indicator = allIndicators.get(fieldName);
                System.out.printf("  Field: '%s'\n", fieldName);
                System.out.printf("    SQL: '%s'\n", indicator.sql);
                System.out.printf("    Tables: %s\n", java.util.Arrays.toString(indicator.tables));
                System.out.println();
            }
            
            // Test getting a specific indicator
            System.out.println("Testing getIndicator() method:");
            JdbcWebServerMain.Indicator testIndicator = JdbcWebServerMain.getIndicator("field name");
            if (testIndicator != null) {
                System.out.printf("✓ Found indicator for 'field name': sql='%s', tables=%s\n", 
                    testIndicator.sql, java.util.Arrays.toString(testIndicator.tables));
            } else {
                System.out.println("✗ Could not find indicator for 'field name'");
            }
            
            // Test getting a non-existent indicator
            JdbcWebServerMain.Indicator nonExistent = JdbcWebServerMain.getIndicator("non-existent");
            if (nonExistent == null) {
                System.out.println("✓ Correctly returned null for non-existent indicator");
            } else {
                System.out.println("✗ Should have returned null for non-existent indicator");
            }
            
        } else {
            System.out.println("✗ Failed to load indicators!");
        }
    }
}
